apiVersion: v1
kind: Service
metadata:
  name: relational-db-service
spec:
  selector:
    app: relational-db
  ports:
    - protocol: TCP
      port: 5432
      targetPort: 5432
  clusterIP: None
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: relational-db-statefulset
spec:
  serviceName: relational-db-service
  replicas: 1
  selector:
    matchLabels:
      app: relational-db
  template:
    metadata:
      labels:
        app: relational-db
    spec:
      containers:
        - name: relational-db
          image: postgres:15.2-alpine
          env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: postgres_user
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: postgres_password
          args: ["-c", "max_connections=250"]
          ports:
            - containerPort: 5432
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: db-storage
              subPath: postgres
  volumeClaimTemplates:
    - metadata:
        name: db-storage
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            # Adjust the storage request size as needed.
            storage: 10Gi
