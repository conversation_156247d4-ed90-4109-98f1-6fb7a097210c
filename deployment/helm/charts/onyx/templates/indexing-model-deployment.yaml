apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "onyx-stack.fullname" . }}-indexing-model
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.indexCapability.replicaCount }}
  selector:
    matchLabels:
      {{- include "onyx-stack.selectorLabels" . | nindent 6 }}
      {{- if .Values.indexCapability.deploymentLabels }}
      {{- toYaml .Values.indexCapability.deploymentLabels | nindent 6 }}
      {{- end }}
  template:
    metadata:
      {{- with .Values.indexCapability.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "onyx-stack.labels" . | nindent 8 }}
        {{- with .Values.indexCapability.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      containers:
      - name: {{ .Values.indexCapability.name }}
        image: "{{ .Values.indexCapability.image.repository }}:{{ .Values.indexCapability.image.tag | default .Chart.AppVersion }}"           
        imagePullPolicy: {{ .Values.indexCapability.image.pullPolicy }}
        command: [ "uvicorn", "model_server.main:app", "--host", "0.0.0.0", "--port", "{{ .Values.indexCapability.containerPorts.server }}", "--limit-concurrency", "{{ .Values.indexCapability.limitConcurrency }}" ]
        ports:
        - name: model-server
          containerPort: {{ .Values.indexCapability.containerPorts.server }}
          protocol: TCP
        envFrom:
          - configMapRef:
              name: {{ .Values.config.envConfigMapName }}
        env:
          - name: INDEXING_ONLY
            value: "{{ default "True" .Values.indexCapability.indexingOnly }}"
          {{- include "onyx-stack.envSecrets" . | nindent 10}}
        volumeMounts:
        {{- range .Values.indexCapability.volumeMounts }}
        - name: {{ .name }}
          mountPath: {{ .mountPath }}
        {{- end }}
      volumes:
      {{- range .Values.indexCapability.volumes }}
      - name: {{ .name }}
        persistentVolumeClaim:
          claimName: {{ .persistentVolumeClaim.claimName }}
      {{- end }}
