apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "onyx-stack.fullname" . }}-test-connection"
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "onyx-stack.fullname" . }}-webserver:{{ .Values.webserver.service.servicePort }}']
  restartPolicy: Never
