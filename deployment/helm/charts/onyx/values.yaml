# Default values for onyx-stack.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

postgresql:
  primary:
    persistence:
      storageClass: ""
      size: 5Gi
  enabled: true
  auth:
    existingSecret: onyx-secrets
    secretKeys:
      # overwriting as postgres typically expects 'postgres-password'
      adminPasswordKey: postgres_password

vespa:
  name: da-vespa-0
  service:
    name: vespa-service
  volumeClaimTemplates:
    - metadata:
        name: vespa-storage
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 1Gi
        storageClassName: ""
  enabled: true
  replicaCount: 1
  image:
    repository: vespa
    pullPolicy: IfNotPresent
    tag: "8.277.17"
  podAnnotations: {}
  podLabels:
    app: vespa
    app.kubernetes.io/instance: onyx
    app.kubernetes.io/name: vespa
  securityContext:
    privileged: true
    runAsUser: 0
  resources:
    # The Vespa Helm chart specifies default resources, which are quite modest. We override
    # them here to increase chances of the chart running successfully.
    requests:
      cpu: 1500m
      memory: 4000Mi
    limits:
      cpu: 1500m
      memory: 4000Mi

persistent:
  storageClassName: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

inferenceCapability:
  service:
    portName: modelserver
    type: ClusterIP
    servicePort: 9000
    targetPort: 9000
  pvc:
    name: inference-model-pvc
    accessModes:
      - ReadWriteOnce
    storage: 3Gi
  name: inference-model-server
  replicaCount: 1
  labels:
    - key: app
      value: inference-model-server
  image:
    repository: onyxdotapp/onyx-model-server
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
    pullPolicy: IfNotPresent
  containerPorts:
    server: 9000
  volumeMounts:
    - name: inference-model-storage
      mountPath: /root/.cache
  volumes:
    - name: inference-model-storage
      persistentVolumeClaim:
        claimName: inference-model-pvc
  podLabels:
    - key: app
      value: inference-model-server

indexCapability:
  service:
    portName: modelserver
    type: ClusterIP
    servicePort: 9000
    targetPort: 9000
  replicaCount: 1
  name: indexing-model-server
  deploymentLabels:
    app: indexing-model-server
  podLabels:
    app: indexing-model-server
  indexingOnly: "True"
  podAnnotations: {}
  containerPorts:
    server: 9000
  volumeMounts:
    - name: indexing-model-storage
      mountPath: /root/.cache
  volumes:
    - name: indexing-model-storage
      persistentVolumeClaim:
        claimName: indexing-model-storage
  indexingModelPVC:
    name: indexing-model-storage
    accessMode: "ReadWriteOnce"
    storage: "3Gi"
  image:
    repository: onyxdotapp/onyx-model-server
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
    pullPolicy: IfNotPresent
  limitConcurrency: 10
config:
  envConfigMapName: env-configmap

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

nginx:
  containerPorts:
    http: 1024
  extraEnvVars:
    - name: DOMAIN
      value: localhost
  service:
    ports:
      http: 80
      onyx: 3000
    targetPort:
      http: http
      onyx: http

  existingServerBlockConfigmap: onyx-nginx-conf

webserver:
  replicaCount: 1
  image:
    repository: onyxdotapp/onyx-web-server
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  deploymentLabels:
    app: web-server
  podAnnotations: {}
  podLabels:
    app: web-server
  podSecurityContext:
    {}
    # fsGroup: 2000

  securityContext:
    {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  containerPorts:
    server: 3000

  service:
    type: ClusterIP
    servicePort: 3000
    targetPort: http

  resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # Additional volumes on the output Deployment definition.
  volumes: []
  # - name: foo
  #   secret:
  #     secretName: mysecret
  #     optional: false

  # Additional volumeMounts on the output Deployment definition.
  volumeMounts: []
  # - name: foo
  #   mountPath: "/etc/foo"
  #   readOnly: true

  nodeSelector: {}
  tolerations: []
  affinity: {}

api:
  replicaCount: 1
  image:
    repository: onyxdotapp/onyx-backend
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  deploymentLabels:
    app: api-server
  podAnnotations: {}
  podLabels:
    scope: onyx-backend
    app: api-server

  containerPorts:
    server: 8080

  podSecurityContext:
    {}
    # fsGroup: 2000

  securityContext:
    {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  service:
    type: ClusterIP
    servicePort: 8080
    targetPort: api-server-port
    portName: api-server-port

  resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  #  requests:
  #    cpu: 1000m  # Requests 1 CPU core
  #    memory: 1Gi  # Requests 1 GiB of memory
  #  limits:
  #    cpu: 2000m  # Limits to 2 CPU cores
  #    memory: 2Gi  # Limits to 2 GiB of memory

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # Additional volumes on the output Deployment definition.
  volumes: []
  # - name: foo
  #   secret:
  #     secretName: mysecret
  #     optional: false

  # Additional volumeMounts on the output Deployment definition.
  volumeMounts: []
  # - name: foo
  #   mountPath: "/etc/foo"
  #   readOnly: true

  nodeSelector: {}
  tolerations: []

background:
  replicaCount: 1
  image:
    repository: onyxdotapp/onyx-backend
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  podAnnotations: {}
  podLabels:
    scope: onyx-backend
    app: background
  deploymentLabels:
    app: background
  podSecurityContext:
    {}
    # fsGroup: 2000
  securityContext:
    privileged: true
    runAsUser: 0
  enableMiniChunk: "true"
  resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  #  requests:
  #    cpu: 1000m  # Requests 1 CPU core
  #    memory: 1Gi  # Requests 1 GiB of memory
  #  limits:
  #    cpu: 2000m  # Limits to 2 CPU cores
  #    memory: 2Gi  # Limits to 2 GiB of memory

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # Additional volumes on the output Deployment definition.
  volumes: []
  # - name: foo
  #   secret:
  #     secretName: mysecret
  #     optional: false

  # Additional volumeMounts on the output Deployment definition.
  volumeMounts: []
  # - name: foo
  #   mountPath: "/etc/foo"
  #   readOnly: true

  nodeSelector: {}
  tolerations: []
  affinity: {}

redis:
  enabled: true
  architecture: standalone
  commonConfiguration: |-
    # Enable AOF https://redis.io/topics/persistence#append-only-file
    appendonly no
    # Disable RDB persistence, AOF persistence already enabled.
    save ""
  master:
    replicaCount: 1
    image:
      registry: docker.io
      repository: bitnami/redis
      tag: "7.4.0"
      pullPolicy: IfNotPresent
    persistence:
      enabled: false
  service:
    type: ClusterIP
    port: 6379
  auth:
    existingSecret: onyx-secrets
    existingSecretPasswordKey: redis_password

# ingress:
#  enabled: false
#  className: ""
#  annotations: {}
#    # kubernetes.io/ingress.class: nginx
#    # kubernetes.io/tls-acme: "true"
#  hosts:
#    - host: chart-example.local
#      paths:
#        - path: /
#          pathType: ImplementationSpecific
#  tls: []
#  #  - secretName: chart-example-tls
#  #    hosts:
#  #      - chart-example.local


auth:
  # existingSecret onyx-secret for storing smtp, oauth, slack, and other secrets
  # keys are lowercased version of env vars (e.g. SMTP_USER -> smtp_user)
  existingSecret: ""
  # optionally override the secret keys to reference in the secret
  # this is used to populate the env vars in individual deployments
  # the values here reference the keys in secrets below
  secretKeys:
    postgres_password: "postgres_password"
    smtp_pass: ""
    oauth_client_id: ""
    oauth_client_secret: ""
    oauth_cookie_secret: ""
    redis_password: "redis_password"
  # will be overridden by the existingSecret if set
  secretName: "onyx-secrets"
  # set values as strings, they will be base64 encoded
  # this is used to populate the secrets yaml
  secrets:
    postgres_password: "postgres"
    smtp_pass: ""
    oauth_client_id: ""
    oauth_client_secret: ""
    oauth_cookie_secret: ""
    redis_password: "password"

configMap:
  # Change this for production uses unless Onyx is only accessible behind VPN
  AUTH_TYPE: "disabled"
  # 1 Day Default
  SESSION_EXPIRE_TIME_SECONDS: "86400"
  # Can be something like onyx.app, as an extra double-check
  VALID_EMAIL_DOMAINS: ""
  # For sending verification emails, if unspecified then defaults to 'smtp.gmail.com'
  SMTP_SERVER: ""
  # For sending verification emails, if unspecified then defaults to '587'
  SMTP_PORT: ""
# '<EMAIL>'
  SMTP_USER: ""
  # 'your-gmail-password'
  # SMTP_PASS: ""
  # '<EMAIL>' SMTP_USER missing used instead
  EMAIL_FROM: ""
  # Gen AI Settings
  GEN_AI_MAX_TOKENS: ""
  QA_TIMEOUT: "60"
  MAX_CHUNKS_FED_TO_CHAT: ""
  DISABLE_LLM_DOC_RELEVANCE: ""
  DISABLE_LLM_CHOOSE_SEARCH: ""
  DISABLE_LLM_QUERY_REPHRASE: ""
  # Query Options
  DOC_TIME_DECAY: ""
  HYBRID_ALPHA: ""
  EDIT_KEYWORD_QUERY: ""
  MULTILINGUAL_QUERY_EXPANSION: ""
  LANGUAGE_HINT: ""
  LANGUAGE_CHAT_NAMING_HINT: ""
  QA_PROMPT_OVERRIDE: ""
  # Internet Search Tool
  BING_API_KEY: ""
  # Don't change the NLP models unless you know what you're doing
  EMBEDDING_BATCH_SIZE: ""
  DOCUMENT_ENCODER_MODEL: ""
  NORMALIZE_EMBEDDINGS: ""
  ASYM_QUERY_PREFIX: ""
  ASYM_PASSAGE_PREFIX: ""
  DISABLE_RERANK_FOR_STREAMING: ""
  MODEL_SERVER_PORT: ""
  MIN_THREADS_ML_MODELS: ""
  # Indexing Configs
  VESPA_SEARCHER_THREADS: ""
  NUM_INDEXING_WORKERS: ""
  DISABLE_INDEX_UPDATE_ON_SWAP: ""
  DASK_JOB_CLIENT_ENABLED: ""
  CONTINUE_ON_CONNECTOR_FAILURE: ""
  EXPERIMENTAL_CHECKPOINTING_ENABLED: ""
  CONFLUENCE_CONNECTOR_LABELS_TO_SKIP: ""
  JIRA_API_VERSION: ""
  GONG_CONNECTOR_START_TIME: ""
  NOTION_CONNECTOR_ENABLE_RECURSIVE_PAGE_LOOKUP: ""
  # OnyxBot SlackBot Configs
  DANSWER_BOT_DISABLE_DOCS_ONLY_ANSWER: ""
  DANSWER_BOT_DISPLAY_ERROR_MSGS: ""
  DANSWER_BOT_RESPOND_EVERY_CHANNEL: ""
  DANSWER_BOT_DISABLE_COT: ""
  NOTIFY_SLACKBOT_NO_ANSWER: ""
  # Logging
  # Optional Telemetry, please keep it on (nothing sensitive is collected)? <3
  # https://docs.onyx.app/more/telemetry
  DISABLE_TELEMETRY: ""
  LOG_LEVEL: ""
  LOG_ALL_MODEL_INTERACTIONS: ""
  LOG_DANSWER_MODEL_INTERACTIONS: ""
  LOG_VESPA_TIMING_INFORMATION: ""
  # Shared or Non-backend Related
  WEB_DOMAIN: "http://localhost:3000"
  # DOMAIN used by nginx
  DOMAIN: "localhost"
  # Chat Configs
  HARD_DELETE_CHATS: ""
