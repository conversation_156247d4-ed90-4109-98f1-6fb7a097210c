apiVersion: apps/v1
kind: Deployment
metadata:
  name: background-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: background
  template:
    metadata:
      labels:
        app: background
    spec:
      containers:
        - name: background
          image: onyxdotapp/onyx-backend:latest
          imagePullPolicy: IfNotPresent
          command:
            - "/bin/sh"
            - "-c"
            - |
              if [ -f /etc/ssl/certs/custom-ca.crt ]; then
                update-ca-certificates;
              fi &&
              /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: redis_password
          envFrom:
            - configMapRef:
                name: env-configmap
      # Uncomment the following lines if you need to include a custom CA certificate
      # This section allows for the inclusion of a custom CA certificate
      # If a custom CA certificate is present, it updates the system's CA certificates
      # This is useful for environments with self-signed or internal CA certificates
      # The certificate is mounted as a volume and the container checks for its presence
      # Optional volume mount for CA certificate
      #   volumeMounts:
      #     - name: my-ca-cert-volume
      #       mountPath: /etc/ssl/certs/custom-ca.crt
      #       subPath: my-ca.crt

      # Optional volume for CA certificate
      # volumes:
      #   - name: my-cas-cert-volume
      #     secret:
      #       secretName: my-ca-cert
      #       items:
      #         - key: my-ca.crt
      #           path: my-ca.crt

      # Uncomment if you are using IAM auth for Postgres
      #     volumeMounts:
      #       - name: bundle-pem
      #         mountPath: "/app/certs"
      #         readOnly: true
      # volumes:
      #   - name: bundle-pem
      #     secret:
      #       secretName: bundle-pem-secret
