# Fill in the values and copy the contents of this file to .env in the deployment directory.
# Some valid default values are provided where applicable, delete the variables which you don't set values for.
# This is only necessary when using the docker-compose.prod.yml compose file.


# Could be something like onyx.companyname.com
WEB_DOMAIN=http://localhost:3000


# NOTE: Generative AI configurations are done via the UI now


# The following are for configuring User Authentication, supported flows are:
# disabled
# basic (standard username / password)
# google_oauth (login with google/gmail account)
# oidc (only in Onyx enterprise edition)
# saml (only in Onyx enterprise edition)
AUTH_TYPE=google_oauth

# Set the values below to use with Google OAuth
GOOGLE_OAUTH_CLIENT_ID=
GOOGLE_OAUTH_CLIENT_SECRET=
SECRET=

# if using basic auth and you want to require email verification, 
# then uncomment / set the following
#REQUIRE_EMAIL_VERIFICATION=true
#SMTP_USER=<EMAIL>
#SMTP_PASS=your-gmail-password

# The below are only needed if you aren't using gmail as your SMTP
#SMTP_SERVER=  
#SMTP_PORT=
# When missing SMTP_USER, this is used instead
#EMAIL_FROM=

# OpenID Connect (OIDC)
#OPENID_CONFIG_URL=

# SAML config directory for OneLogin compatible setups
#SAML_CONF_DIR=


# How long before user needs to reauthenticate, default to 7 days. (cookie expiration time)
SESSION_EXPIRE_TIME_SECONDS=604800


# Use the below to specify a list of allowed user domains, only checked if user Auth is turned on
# e.g. `VALID_EMAIL_DOMAINS=example.com,example.org` will only allow users
# with an @example.com or an @example.org email
#VALID_EMAIL_DOMAINS=


# Default values here are what Postgres uses by default, feel free to change.
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
