import { Tag } from "../types";

export async function getTagsForAssistant(
  assistantId: number,
  matchPattern: string | null = null,
  sources: string[] | null = null
): Promise<Tag[]> {
  if (!assistantId) {
    return [];
  }

  const params = new URLSearchParams();
  params.append("persona_id", assistantId.toString());
  if (matchPattern) params.append("match_pattern", matchPattern);
  if (sources) sources.forEach((source) => params.append("sources", source));

  const response = await fetch(`/api/persona/tags?${params.toString()}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('API error response:', errorText, 'Status:', response.status);
    throw new Error(`Failed to fetch tags for assistant: ${response.status} ${response.statusText}`);
  }

  try {
    const data = await response.json();
    console.log('API response data:', data);

    if (!data || !data.tags) {
      console.error('Invalid response format:', data);
      throw new Error('Invalid response format from server');
    }

    return data.tags;
  } catch (error) {
    console.error('Error parsing response:', error);
    throw new Error('Failed to parse response from server');
  }
}
