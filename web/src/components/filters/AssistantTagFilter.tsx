import React, { useState, useEffect } from "react";
import { Tag } from "@/lib/types";
import { getTagsForAssistant } from "@/lib/tags/assistantTags";
import { TagFilter } from "@/components/search/filtering/TagFilter";

interface AssistantTagFilterProps {
  assistantId: number;
  selectedTags: Tag[];
  setSelectedTags: React.Dispatch<React.SetStateAction<Tag[]>>;
}

export function AssistantTagFilter({
  assistantId,
  selectedTags,
  setSelectedTags,
}: AssistantTagFilterProps) {
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTags = async () => {
      if (!assistantId || isNaN(assistantId) || assistantId <= 0) {
        console.log('Invalid assistantId:', assistantId);
        setAvailableTags([]);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('Fetching tags for assistant ID:', assistantId);
        const tags = await getTagsForAssistant(assistantId);
        console.log('Received tags:', tags);
        setAvailableTags(tags);
      } catch (err) {
        console.error("Error fetching tags for assistant:", err);
        setError("Failed to load tags");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, [assistantId]);

  if (isLoading) {
    return <div className="text-sm text-gray-500">Loading tags...</div>;
  }

  if (error) {
    return <div className="text-sm text-red-500">{error}</div>;
  }

  if (availableTags.length === 0) {
    return <div className="text-sm text-gray-500">No tags available for this assistant</div>;
  }

  return (
    <TagFilter
      tags={availableTags}
      selectedTags={selectedTags}
      setSelectedTags={setSelectedTags}
    />
  );
}
