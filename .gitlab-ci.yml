workflow:
  name: '$CI_PROJECT_NAME pipeline: $CI_COMMIT_TAG'
  rules:
    - if: $CI_COMMIT_BRANCH
      when: never
    - if: '$CI_COMMIT_TAG =~ /^v0\.[0-9]+\.[0-9]+$/'

variables:
  BACKEND: "$CI_PROJECT_NAME-backend"
  WEB: "$CI_PROJECT_NAME-web"
  MODEL_SERVER: "$CI_PROJECT_NAME-model-server"

stages:
  - build
  - deploy

default:
  id_tokens:
    OIDC_TOKEN:
      aud: https://git.centelon.com
  before_script:
    - |
      echo "########### Docker Login ###########"
      mkdir ~/.docker 
      echo $DOCKER_AUTH_CONFIG > ~/.docker/config.json
    - |
      echo "####### setting up aws credentials #######"
      echo $OIDC_TOKEN > $AWS_WEB_IDENTITY_TOKEN_FILE
  tags:
    - shared
  

backend:
  stage: build
  image: docker:cli
  variables:
    AWS_ROLE_ARN: $GITLAB_ECR_ROLE_ARN
    ONYX_VERSION: $CI_COMMIT_TAG
  script:
    - |
      echo "build and push image to $BACKEND ecr 📦📦📦"
      docker buildx build --platform linux/amd64 --file ./backend/Dockerfile --build-arg ONYX_VERSION --tag $ECR_REG/$BACKEND:$CI_COMMIT_TAG --tag $ECR_REG/$BACKEND:latest --provenance=false --push ./backend

web:
  stage: build
  image: docker:cli
  variables:
    AWS_ROLE_ARN: $GITLAB_ECR_ROLE_ARN
    ONYX_VERSION: $CI_COMMIT_TAG
    NODE_OPTIONS: "--max-old-space-size=8192"
  script:
    - |
      echo "build and push image to $WEB ecr 📦📦📦"
      docker buildx build --platform linux/amd64 --file ./web/Dockerfile --build-arg NODE_OPTIONS --build-arg ONYX_VERSION --tag $ECR_REG/$WEB:$CI_COMMIT_TAG --tag $ECR_REG/$WEB:latest --provenance=false --push ./web

model-server:
  stage: build
  image: docker:cli
  variables:
    AWS_ROLE_ARN: $GITLAB_ECR_ROLE_ARN
    DANSWER_VERSION: $CI_COMMIT_TAG
  script:
    - |
      echo "build and push image to $MODEL_SERVER ecr 📦📦📦"
      docker buildx build --platform linux/amd64 --file ./backend/Dockerfile.model_server --build-arg DANSWER_VERSION --tag $ECR_REG/$MODEL_SERVER:$CI_COMMIT_TAG --tag $ECR_REG/$MODEL_SERVER:latest --provenance=false --push ./backend

deploy:service:
  stage: deploy
  image: 346264802683.dkr.ecr.ap-southeast-2.amazonaws.com/ansible-ci:latest
  dependencies: []
  variables:
    ANSIBLE_HOST_KEY_CHECKING: "false"
    ANSIBLE_FORCE_COLOR: "true"
    ANSIBLE_STDOUT_CALLBACK: "yaml"
    ANSIBLE_LOAD_CALLBACK_PLUGINS: "true"
    AWS_ROLE_ARN: $GITLAB_SSM_ROLE_ARN
    ATHENA_ENV: "dev"
  before_script:
    - |
      echo "####### setting up aws credentials #######"
      echo $OIDC_TOKEN > $AWS_WEB_IDENTITY_TOKEN_FILE
  script:
    - |
      echo "Deploying Service to $ATHENA_ENV server 🚀🚀🚀"
      ansible-playbook -i .gitlab/pipeline/inventory.cfg -e target=$ATHENA_ENV .gitlab/pipeline/deploy-service.yml
