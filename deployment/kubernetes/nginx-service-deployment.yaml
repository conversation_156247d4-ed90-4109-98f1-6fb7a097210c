apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 80
    - name: onyx
      protocol: TCP
      port: 3000
      targetPort: 80
  type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
        - name: nginx
          image: nginx:1.23.4-alpine
          ports:
            - containerPort: 80
          env:
            - name: DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: env-configmap
                  key: DOMAIN
          volumeMounts:
            - name: nginx-conf
              mountPath: /etc/nginx/conf.d
          command:
            - /bin/sh
            - -c
            - |
              while :; do sleep 6h & wait $$!; nginx -s reload; done & nginx -g "daemon off;"
      volumes:
        - name: nginx-conf
          configMap:
            name: nginx-configmap
