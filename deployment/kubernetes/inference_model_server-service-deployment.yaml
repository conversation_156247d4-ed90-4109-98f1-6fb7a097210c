apiVersion: v1
kind: Service
metadata:
  name: inference-model-server-service
spec:
  selector:
    app: inference-model-server
  ports:
    - name: inference-model-server-port
      protocol: TCP
      port: 9000
      targetPort: 9000
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: inference-model-server-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inference-model-server
  template:
    metadata:
      labels:
        app: inference-model-server
    spec:
      containers:
        - name: inference-model-server
          image: onyxdotapp/onyx-model-server:latest
          imagePullPolicy: IfNotPresent
          command:
            [
              "uvicorn",
              "model_server.main:app",
              "--host",
              "0.0.0.0",
              "--port",
              "9000",
            ]
          ports:
            - containerPort: 9000
          envFrom:
            - configMapRef:
                name: env-configmap
