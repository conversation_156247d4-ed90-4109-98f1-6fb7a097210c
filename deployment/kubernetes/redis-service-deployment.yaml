apiVersion: v1
kind: Service
metadata:
  name: redis-service
spec:
  selector:
    app: redis
  ports:
    - name: redis
      protocol: TCP
      port: 6379
      targetPort: 6379
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7.4-alpine
          ports:
            - containerPort: 6379
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: redis_password
          command: ["redis-server"]
          args:
            # save and appendonly are not strictly necessary because kuberne<PERSON> doesn't mount
            # /data silently like docker, but add the save and appendonly for consistency
            [
              "--requirepass",
              "$(REDIS_PASSWORD)",
              "--save",
              "",
              "--appendonly",
              "no",
            ]
