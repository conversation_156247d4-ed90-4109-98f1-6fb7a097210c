# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
.env.sentry-build-plugin
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# playwright testing temp files
/admin_auth.json
/user_auth.json
/build-archive.log
/test-results
