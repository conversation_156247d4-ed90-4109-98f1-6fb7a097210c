apiVersion: v1
kind: Service
metadata:
  name: web-server-service
spec:
  selector:
    app: web-server
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-server-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: web-server
  template:
    metadata:
      labels:
        app: web-server
    spec:
      containers:
        - name: web-server
          image: onyxdotapp/onyx-web-server:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          # There are some extra values since this is shared between services
          # There are no conflicts though, extra env variables are simply ignored
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: redis_password
          envFrom:
            - configMapRef:
                name: env-configmap
