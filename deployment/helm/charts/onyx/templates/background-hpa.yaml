{{- if .Values.background.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "onyx-stack.fullname" . }}-background
  labels:
    {{- include "onyx-stack.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "onyx-stack.fullname" . }}
  minReplicas: {{ .Values.background.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.background.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.background.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.background.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.background.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.background.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
