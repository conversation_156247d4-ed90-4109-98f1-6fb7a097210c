apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-configmap
data:
  nginx.conf: |
    upstream api_server {
        server api-server-service:80 fail_timeout=0;
    }

    upstream web_server {
        server web-server-service:80 fail_timeout=0;
    }

    server {
        listen 80;
        server_name $$DOMAIN;

        client_max_body_size 5G;    # Maximum upload size

        location ~ ^/api(.*)$ {
            rewrite ^/api(/.*)$ $1 break;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header Host $host;
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_redirect off;
            proxy_pass http://api_server;
        }

        location / {
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header Host $host;
            proxy_http_version 1.1;
            proxy_redirect off;
            proxy_pass http://web_server;
        }
    }
