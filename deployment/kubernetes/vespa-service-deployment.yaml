apiVersion: v1
kind: Service
metadata:
  name: document-index-service
spec:
  selector:
    app: vespa
  ports:
    - name: vespa-tenant-port
      protocol: TCP
      port: 19071
      targetPort: 19071
    - name: vespa-port
      protocol: TCP
      port: 8081
      targetPort: 8081
  type: LoadBalancer
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vespa
  labels:
    app: vespa
spec:
  replicas: 1
  serviceName: vespa
  selector:
    matchLabels:
      app: vespa
  template:
    metadata:
      labels:
        app: vespa
    spec:
      containers:
      - name: vespa
        image: vespaengine/vespa:8.277.17
        imagePullPolicy: IfNotPresent
        securityContext:
          privileged: true
          runAsUser: 0
        ports:
        - containerPort: 19071
        - containerPort: 8081
        readinessProbe:
          httpGet:
            path: /state/v1/health
            port: 19071
            scheme: HTTP
        volumeMounts:
        - name: vespa-volume
          mountPath: /opt/vespa/var/
  volumeClaimTemplates:
  - metadata:
      name: vespa-volume
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          # Adjust the storage request size as needed.
          storage: 50Gi
