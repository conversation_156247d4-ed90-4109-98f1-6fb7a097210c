apiVersion: v1
kind: Service
metadata:
  name: api-server-service
spec:
  selector:
    app: api-server
  ports:
    - name: api-server-port
      protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-server-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api-server
  template:
    metadata:
      labels:
        app: api-server
    spec:
      containers:
        - name: api-server
          image: onyxdotapp/onyx-backend:latest
          imagePullPolicy: IfNotPresent
          command:
            - "/bin/sh"
            - "-c"
            - |
              alembic upgrade head &&
              echo "Starting Onyx Api Server" &&
              uvicorn onyx.main:app --host 0.0.0.0 --port 8080
          ports:
            - containerPort: 8080
          # There are some extra values since this is shared between services
          # There are no conflicts though, extra env variables are simply ignored
          env:
            - name: OAUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: google_oauth_client_id
            - name: OAUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: google_oauth_client_secret
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: onyx-secrets
                  key: redis_password
          envFrom:
            - configMapRef:
                name: env-configmap
      # Uncomment if you are using IAM auth for Postgres
      #     volumeMounts:
      #       - name: bundle-pem
      #         mountPath: "/app/certs"
      #         readOnly: true
      # volumes:
      #   - name: bundle-pem
      #     secret:
      #       secretName: bundle-pem-secret
