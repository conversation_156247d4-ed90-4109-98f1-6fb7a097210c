@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    --input-text: #6c6c6b;

    /* ---------------------------------
    
     * 1. Single set of neutral values
     * --------------------------------- */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-125: #f1f2f4; /* You had background-125 & text-125 as #f1f2f4 */
    --neutral-150: #eaeaea;
    --neutral-200: #d4d4d4;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-600-border-dark: #525252;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #111827;
    --neutral-950: #0a0a0a;

    --hover: var(--neutral-200);

    /* -------------------------------------------------------
     * 2. Keep special, custom, or near-duplicate background
     * ------------------------------------------------------- */
    --background: #fefcfa; /* slightly off-white, keep it */
    --input-background: #fefcfa;
    --input-border: #f1eee8;
    --text-text: #f4f2ed;
    --background-dark: #e9e6e0;
    --new-background: #ebe7de;
    --new-background-light: #d9d1c0;
    --background-chatbar: #f5f3ee;
    --background-chat-hover: #e6e3dd;
    --background-chat-selected: #e6e3dd;
    --background-sidebar: #f5f3ee;

    /* borders & input (keep if not same as neutral) */
    --border: #e5e7eb; /* slightly different than #e5e5e5 */
    --border-light: #f5f5f5;
    --border-medium: #d4d4d4;
    --border-strong: #a3a3a3;
    --border-dark: #525252;
    --input: 0 0% 89.8%;

    /* Additional single-purpose color vars remain */
    --foreground: 0 0% 3.9%;
    --card: #ffffff;
    --card-foreground: #0c0c0c;
    --popover: #ffffff;
    --popover-foreground: #0c0c0c;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --accent-background: #f0eee8;
    --accent-background-hovered: #e5e3dd;
    --accent-background-selected: #eae8e2;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;

    /* Some charts, links, etc. */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --link: #3b82f6;
    --link-hover: #1d4ed8;

    /* Status, success, alerts, etc. */
    --error: #f87171;
    --undo: #ef4444;
    --success: #030706;
    --light-success: #22c55e;
    --alert: #f59e0b;
    --accent-hover: #4f46e5;
    --black: #000000;
    --white: #ffffff;

    /* Code syntax highlighting */
    --code-text: #d4d4d4;
    --token-comment: #608b4e;
    --token-punctuation: #d4d4d4;
    --token-property: #569cd6;
    --token-selector: #e07b53;
    --token-atrule: #d18ad8;
    --token-function: #f0e68c;
    --token-regex: #9cdcfe;
    --token-attr-name: #9cdcfe;

    /* Misc. custom */
    --non-selectable: #f8d7da;
    --non-selectable-border: #f5c2c7;
    --highlight-text: rgba(255, 245, 180, 0.5);
    --user-bubble: #f1eee8;
    --ai-bubble: #272a2d;
    --document-color: #f43f5e;
    --scrollbar-track: #f9fafb;
    --scrollbar-thumb: #e5e7eb;
    --scrollbar-thumb-hover: #d1d5db;
    --scrollbar-dark-thumb: #989a9c;
    --scrollbar-dark-thumb-hover: #c7cdd2;

    /* agent references */
    --agent-sidebar: #be5d0e;
    --agent-hovered: #d16b10;
    --agent: #e47011;
    --lighter-agent: #f59e0b;

    /* Consolidated color variables */
    --neutral-100-border-light: #f5f5f5;
    --neutral-300-border-medium: #d4d4d4;
    --neutral-400-border-strong: #a3a3a3;
    --background-input-background: #fefcfa;
    --background-chatbar-sidebar: #f5f3ee;
    --background-chat-hover-selected: #e6e3dd;
    --white-card-popover: #ffffff;
    --card-popover-foreground: #0c0c0c;

    /* Near-duplicates consolidated */
    --neutral-200-border: #e5e6e6; /* Average of #e5e5e5 and #e5e7eb */
    --off-white: #f3f1ec; /* Average of #f4f2ed, #f5f3ee, #f1eee8, #e9e6e0, #ebe7de */

    /* Update references to use new consolidated variables */
    --neutral-100: var(--neutral-100-border-light);
    --border-light: var(--neutral-100-border-light);
    --neutral-300: var(--neutral-300-border-medium);
    --border-medium: var(--neutral-300-border-medium);
    --neutral-400: var(--neutral-400-border-strong);
    --border-strong: var(--neutral-400-border-strong);
    --neutral-600: var(--neutral-600-border-dark);
    --border-dark: var(--neutral-600-border-dark);
    --background: var(--background-input-background);
    --input-background: var(--background-input-background);
    --background-chatbar: var(--background-chatbar-sidebar);
    --background-sidebar: var(--background-chatbar-sidebar);
    --background-chat-hover: var(--background-chat-hover-selected);
    --background-chat-selected: var(--background-chat-hover-selected);
    --white: var(--white-card-popover);
    --card: var(--white-card-popover);
    --popover: var(--white-card-popover);
    --card-foreground: var(--card-popover-foreground);
    --popover-foreground: var(--card-popover-foreground);

    /* Update near-duplicates */
    --neutral-200: var(--neutral-200-border);
    --border: var(--neutral-200-border);
    --text-text: var(--off-white);
    --background-dark: var(--off-white);
    --new-background: var(--off-white);
    --user-bubble: var(--off-white);
  }

  .dark {
    --tremor-brand-faint: #eff6ff;
    --tremor-brand-muted: #bfdbfe;
    --tremor-brand-subtle: #60a5fa;
    --tremor-brand-default: #3b82f6;
    --tremor-brand-emphasis: #1d4ed8;
    --tremor-brand-inverted: #ffffff;
    --tremor-background-muted: #f9fafb;
    --tremor-background-subtle: #f3f4f6;
    --tremor-background-default: #ffffff;
    --tremor-background-emphasis: #374151;
    --tremor-content-subtle: #9ca3af;
    --tremor-content-default: #4b5563;
    --tremor-content-emphasis: #374151;
    --tremor-content-strong: #111827;
    --tremor-content-inverted: #ffffff;

    --dark-tremor-brand-faint: #0b1229;
    --dark-tremor-brand-muted: #172554;
    --dark-tremor-brand-subtle: #1e40af;
    --dark-tremor-brand-emphasis: #60a5fa;
    --dark-tremor-brand-inverted: #030712;
    --dark-tremor-background-muted: #131a2b;
    --dark-tremor-background-subtle: #1f2937;
    --dark-tremor-background-default: #111827;
    --dark-tremor-background-emphasis: #d1d5db;
    --dark-tremor-content-subtle: #6b7280;
    --dark-tremor-content-default: #d1d5db;
    --dark-tremor-content-emphasis: #f3f4f6;
    --dark-tremor-content-strong: #f9fafb;
    --dark-tremor-content-inverted: #000000;

    /* ---------------------------------
     * 1. Single set of neutral values
     *    (reversed for dark mode)
     * --------------------------------- */
    --inputoption: "#B4B4B4";
    --neutral-50: #161616; /* darkest */
    --neutral-100-border-light: #1a1a1a;
    --neutral-125: #1f1f1f;
    --neutral-150: #252525;
    --neutral-200-border: #2c2c2c;
    --neutral-300-border-medium: #404040;
    --neutral-400-border-strong: #5a5a5a;
    --neutral-500: #757575;
    --neutral--neutral-600-border-dark: #a0a0a0;
    --neutral-700: #bdbdbd;
    --neutral-800: #d4d4d4;
    --neutral-900: #d4d4d4;
    --neutral-950: #f0f0f0; /* lightest */
    --hover: #2c2c2c;

    --input-text: #b4b4b4;

    --text-darker: #f0f0f0;
    --text-dark: #f0f0f0;
    /* -------------------------------------------------------
     * 2. Special / custom backgrounds (shifted to dark)
     * ------------------------------------------------------- */
    --background-input-background: #1f1f1f;
    --input-background: #303030;
    --input-border: #5a5a5a;

    --text-text: #1d1d1d;
    --background-dark: #252525;

    /* --new-background: #fff; */
    --new-background: #2c2c2c;
    --new-background-light: #383838;

    --background-chatbar-sidebar: #171717;
    --background-chat-hover-selected: #3a3a3a;
    --background-chat-selected: #2f2f2f;
    --background-sidebar: #171717;
    --black: #000000;

    /* --accent-background: #181816; */
    --accent-background: #333333;

    --accent-background-hovered: #2f2f2f;
    --accent-background-selected: #222222;

    --text-darker: #f0f0f0;

    /* borders & input (reversed scales) */
    --neutral-200-border: #5a5a5a; /* was #e5e7eb */
    --neutral-100-border-light: #6a6a6a; /* was #f5f5f5 */
    --neutral-300-border-medium: #4a4a4a; /* was #d4d4d4 */
    --neutral-400-border-strong: #3a3a3a; /* was #a3a3a3 */
    --neutral-600-border-dark: #9ca3af; /* was #525252, now using Tailwind's neutral-400 */
    --input: 0 0% 15%; /* (HSL) approx #262626 for dark input backgrounds */

    /* Additional single-purpose color vars (inverted where sensible) */
    --foreground: 0 0% 95%; /* light text on dark bg (was 0 0% 3.9%) */
    --card: #1f1f1f;
    --card-foreground: #f3f3f3;
    --popover: #1f1f1f;
    --popover-foreground: #f2f2f2;

    /* You can invert "primary"/"secondary" or pick your own scales */
    --primary: 0 0% 80%; /* lighter gray for emphasis */
    --primary-foreground: 0 0% 10%; /* dark text if placed on "primary" */
    --secondary: 0 0% 25%;
    --secondary-foreground: 0 0% 90%;

    --muted: 0 0% 25%;
    --muted-foreground: 0 0% 65%; /* e.g. #aaaaaa */

    --accent: 0 0% 25%;
    --accent-foreground: 0 0% 90%;

    /* Destructive remains red, but you could darken or lighten */
    --destructive: #b91c1c; /* dark-ish red */
    --destructive-foreground: #ffffff;

    --ring: #f2f2f2; /* used for focus rings, keep a light highlight on dark bg */
    --radius: 0.5rem;

    /* Some charts, links, etc. – typically kept as-is, or tweak if desired */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --link: #3b82f6;
    --link-hover: #1d4ed8;

    /* Status, success, alerts, etc. – can keep or adjust as needed */
    --error: #f87171;
    --undo: #ef4444;
    --success: #22c55e; /* bright green stands out on dark */
    --light-success: #86efac;
    --alert: #f59e0b;
    --accent-hover: #4f46e5;
    --black: #ffffff;
    --white-card-popover: #000000;

    /* Code syntax highlighting – typically brighter/lighter for dark backgrounds */
    --code-text: #d4d4d4;
    --token-comment: #608b4e;
    --token-punctuation: #d4d4d4;
    --token-property: #569cd6;
    --token-selector: #e07b53;
    --token-atrule: #d18ad8;
    --token-function: #f0e68c;
    --token-regex: #9cdcfe;
    --token-attr-name: #9cdcfe;

    /* Misc. custom (tweaked to darker equivalents where it makes sense) */
    --non-selectable: #5d1a1f; /* was #f8d7da - if needed on dark BG */
    --non-selectable-border: #4a1215; /* was #f5c2c7 */
    --highlight-text: rgba(
      255,
      245,
      180,
      0.5
    ); /* still stands out as highlight */
    --tw-prose-bold: #fff;

    --off-white: #2a2a2a; /* was #f1eee8 */

    --ai-bubble: #1f1f1f;
    --document-color: #f43f5e; /* keep the same bright accent? */

    /* Scrollbars, etc. */
    --scrollbar-track: #1c1d1e;
    --scrollbar-thumb: #3a3a3a;
    --scrollbar-thumb-hover: #4a4a4a;
    --scrollbar-dark-thumb: #666666;
    --scrollbar-dark-thumb-hover: #909090;

    /* Agent references */
    --agent-sidebar: #be5d0e; /* You can keep or lighten/darken if desired */
    --agent-hovered: #f07c13;
    --agent: #e47011;
    --lighter-agent: #f59e0b;
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
}

.default-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.default-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.default-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.default-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.default-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #888 transparent;
  overflow: overlay;
  overflow-y: scroll;
  overflow-x: hidden;
}

.scrollbar {
  width: 100%;
  height: 100%;
}

/* Styling for textarea scrollbar */
textarea::-webkit-scrollbar {
  width: 8px;
}

textarea::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Styling for textarea resize handle */
textarea {
  resize: vertical;
}

/* For Firefox */
textarea {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

.inputscroll::-webkit-scrollbar-track {
  background: #e5e7eb;
  scrollbar-width: none;
}

::-webkit-scrollbar {
  width: 0px;
  /* Vertical scrollbar width */
  height: 8px;
  /* Horizontal scrollbar height */
}

::-webkit-scrollbar-track {
  background: transparent;
  /* background: theme("colors.scrollbar.track"); */
  /* Track background color */
}

/* Style the scrollbar handle */
::-webkit-scrollbar-thumb {
  background: transparent;
  /* background: theme("colors.scrollbar.thumb"); */
  /* Handle color */
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: transparent;
  /* background: theme("colors.scrollbar.thumb-hover"); */
  /* Handle color on hover */
}

.dark-scrollbar::-webkit-scrollbar-thumb {
  background: transparent;
  /* background: theme("colors.scrollbar.dark.thumb"); */
  /* Handle color */
  border-radius: 10px;
}

.nextjs-portal {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  pointer-events: none !important;
  clip: rect(0, 0, 0, 0) !important;
}

.nextjs-portal * {
  display: none !important;
}

.dark-scrollbar::-webkit-scrollbar-thumb:hover {
  background: transparent;
  /* background: theme("colors.scrollbar.dark.thumb-hover"); */
  /* Handle color on hover */
}

/* Used to create alternatie to React Markdown */
.preserve-lines {
  white-space: pre-wrap;
  /* Preserves whitespace and wraps text */
}

.loading-text {
  display: inline-block;
  color: #e5e5e5;

  background: linear-gradient(
    -90deg,
    #a3a3a3 0%,
    #000000 5%,
    #a3a3a3 10%,
    #a3a3a3 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmerTransition 1.5s ease-out infinite;
}

.dark .loading-text {
  color: #1a1a1a;

  background: linear-gradient(
    -90deg,
    #5c5c5c 0%,
    #ffffff 5%,
    #5c5c5c 10%,
    #5c5c5c 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes shimmerTransition {
  0% {
    background-position: 100% 0;
  }

  100% {
    background-position: -100% 0;
  }
}

.collapsible {
  max-height: 300px;
  transition:
    max-height 0.5s ease-in-out,
    opacity 0.5s ease-in-out;
  opacity: 1;
}

.collapsible-closed {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.prevent-scroll {
  overscroll-behavior-y: none;
}

body {
  overscroll-behavior-y: none;
  overflow-anchor: none;
}

/* Base styles for code blocks */
.prose :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  background-color: theme("colors.code-bg");
  font-size: theme("fontSize.code-sm");
  color: #fff;
}

pre[class*="language-"],
code[class*="language-"] {
  color: theme("colors.code-text");
}

/* Token styles */
.code-line .token.comment,
.code-line .token.prolog,
.code-line .token.doctype,
.code-line .token.cdata {
  color: theme("colors.token-comment");
}

.code-line .token.punctuation,
.code-line .token.operator,
.code-line .token.entity,
.code-line .token.url,
.code-line .language-css .token.string,
.code-line .style .token.string {
  color: theme("colors.token-punctuation");
}

.code-line .token.property,
.code-line .token.tag,
.code-line .token.boolean,
.code-line .token.number,
.code-line .token.constant,
.code-line .token.symbol,
.code-line .token.deleted,
.code-line .token.tag .token.punctuation {
  color: theme("colors.token-property");
}

.code-line .token.selector,
.code-line .token.attr-name,
.code-line .token.string,
.code-line .token.char,
.code-line .token.builtin,
.code-line .token.inserted,
.code-line .token.attr-value,
.code-line .token.attr-value .token.punctuation {
  color: theme("colors.token-selector");
}

.code-line .token.atrule,
.code-line .token.keyword {
  color: theme("colors.token-atrule");
}

.code-line .token.function,
.code-line .token.class-name {
  color: theme("colors.token-function");
}

.code-line .token.regex,
.code-line .token.important,
.code-line .token.variable {
  color: theme("colors.token-regex");
}

.code-line .token.important,
.code-line .token.bold {
  font-weight: theme("fontWeight.token-bold");
}

.code-line .token.italic {
  font-style: theme("fontStyle.token-italic");
}

.code-line .token.entity {
  cursor: help;
}

.code-line .token.attr-name {
  color: theme("colors.token-attr-name");
}

form {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  align-items: start;
}

ol > li > p,
ul > li > p {
  margin-top: 0;
  margin-bottom: 0;
  display: inline;
  /* Make paragraphs inline to reduce vertical space */
}
