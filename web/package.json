{"name": "qa", "version": "1.0.0-dev", "version-comment": "version field must be SemVer or chromatic will barf", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@headlessui/tailwindcss": "^0.2.1", "@phosphor-icons/react": "^2.0.8", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@sentry/nextjs": "^8.50.0", "@sentry/tracing": "^7.120.3", "@stripe/stripe-js": "^4.6.0", "@types/js-cookie": "^3.0.3", "@types/lodash": "^4.17.0", "@types/node": "18.15.11", "@types/prismjs": "^1.26.4", "@types/react": "18.0.32", "@types/react-dom": "18.0.11", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.14", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "favicon-fetch": "^1.0.0", "formik": "^2.2.9", "js-cookie": "^3.0.5", "katex": "^0.16.17", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "mdast-util-find-and-replace": "^3.0.1", "next": "^15.2.0", "next-themes": "^0.4.4", "npm": "^10.8.0", "postcss": "^8.4.31", "posthog-js": "^1.176.0", "prismjs": "^1.29.0", "react": "^18.3.1", "react-datepicker": "^7.6.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-icons": "^4.8.0", "react-loader-spinner": "^5.4.5", "react-markdown": "^9.0.1", "react-select": "^5.8.0", "recharts": "^2.13.1", "rehype-katex": "^7.0.1", "rehype-prism-plus": "^2.0.0", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "semver": "^7.5.4", "sharp": "^0.33.5", "stripe": "^17.0.0", "swr": "^2.1.5", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typescript": "5.0.3", "uuid": "^9.0.1", "vaul": "^1.1.1", "yup": "^1.4.0"}, "devDependencies": {"@chromatic-com/playwright": "^0.10.2", "@tailwindcss/typography": "^0.5.10", "@types/chrome": "^0.0.287", "@types/jest": "^29.5.14", "chromatic": "^11.25.2", "eslint": "^8.48.0", "eslint-config-next": "^14.1.0", "jest": "^29.7.0", "prettier": "2.8.8", "ts-jest": "^29.2.5"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}