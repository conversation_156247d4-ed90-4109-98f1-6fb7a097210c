apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "onyx-stack.fullname" . }}-inference-model
  labels:
    {{- range .Values.inferenceCapability.labels }}
    {{ .key }}: {{ .value }}
    {{- end }}
spec:
  replicas: {{ .Values.inferenceCapability.replicaCount }}
  selector:
    matchLabels:
      {{- range .Values.inferenceCapability.labels }}
      {{ .key }}: {{ .value }}
      {{- end }}
  template:
    metadata:
      labels:
        {{- range .Values.inferenceCapability.podLabels }}
        {{ .key }}: {{ .value }}
        {{- end }}
    spec:
      containers:
      - name: model-server-inference
        image: "{{ .Values.inferenceCapability.image.repository }}:{{ .Values.inferenceCapability.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.inferenceCapability.image.pullPolicy }}
        command: [ "uvicorn", "model_server.main:app", "--host", "0.0.0.0", "--port", "{{ .Values.inferenceCapability.containerPorts.server }}" ]
        ports:
        - name: model-server
          containerPort: {{ .Values.inferenceCapability.containerPorts.server }}
          protocol: TCP
        envFrom:
        - configMapRef:
            name: {{ .Values.config.envConfigMapName }}
        env:
          {{- include "onyx-stack.envSecrets" . | nindent 12}}
        volumeMounts:
        {{- range .Values.inferenceCapability.volumeMounts }}
        - name: {{ .name }}
          mountPath: {{ .mountPath }}
        {{- end }}
      volumes:
      {{- range .Values.inferenceCapability.volumes }}
      - name: {{ .name }}
        persistentVolumeClaim:
          claimName: {{ .persistentVolumeClaim.claimName }}
      {{- end }}
