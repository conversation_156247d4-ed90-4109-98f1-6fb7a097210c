apiVersion: v1
kind: ConfigMap
metadata:
  name: env-configmap
data:
  # Auth Setting, also check the secrets file
  AUTH_TYPE: "disabled" # Change this for production uses unless Onyx is only accessible behind VPN
  ENCRYPTION_KEY_SECRET: "" # This should not be specified directly in the yaml, this is just for reference
  SESSION_EXPIRE_TIME_SECONDS: "86400" # 1 Day Default
  VALID_EMAIL_DOMAINS: "" # Can be something like onyx.app, as an extra double-check
  SMTP_SERVER: "" # For sending verification emails, if unspecified then defaults to 'smtp.gmail.com'
  SMTP_PORT: "" # For sending verification emails, if unspecified then defaults to '587'
  SMTP_USER: "" # '<EMAIL>'
  SMTP_PASS: "" # 'your-gmail-password'
  EMAIL_FROM: "" # '<EMAIL>' SMTP_USER missing used instead
  CORS_ALLOWED_ORIGIN: ""
  # Gen AI Settings
  GEN_AI_MAX_TOKENS: ""
  QA_TIMEOUT: "60"
  MAX_CHUNKS_FED_TO_CHAT: ""
  DISABLE_LLM_DOC_RELEVANCE: ""
  DISABLE_LLM_CHOOSE_SEARCH: ""
  DISABLE_LLM_QUERY_REPHRASE: ""
  # Query Options
  DOC_TIME_DECAY: ""
  HYBRID_ALPHA: ""
  EDIT_KEYWORD_QUERY: ""
  MULTILINGUAL_QUERY_EXPANSION: ""
  LANGUAGE_HINT: ""
  LANGUAGE_CHAT_NAMING_HINT: ""
  QA_PROMPT_OVERRIDE: ""
  # Other Services
  POSTGRES_HOST: "relational-db-service"
  POSTGRES_DEFAULT_SCHEMA: ""
  VESPA_HOST: "document-index-service"
  REDIS_HOST: "redis-service"
  # Internet Search Tool
  BING_API_KEY: ""
  # Don't change the NLP models unless you know what you're doing
  EMBEDDING_BATCH_SIZE: ""
  DOCUMENT_ENCODER_MODEL: ""
  NORMALIZE_EMBEDDINGS: ""
  ASYM_QUERY_PREFIX: ""
  ASYM_PASSAGE_PREFIX: ""
  DISABLE_RERANK_FOR_STREAMING: ""
  MODEL_SERVER_HOST: "inference-model-server-service"
  MODEL_SERVER_PORT: ""
  INDEXING_MODEL_SERVER_HOST: "indexing-model-server-service"
  MIN_THREADS_ML_MODELS: ""
  # Indexing Configs
  VESPA_SEARCHER_THREADS: ""
  NUM_INDEXING_WORKERS: ""
  ENABLED_CONNECTOR_TYPES: ""
  DISABLE_INDEX_UPDATE_ON_SWAP: ""
  DASK_JOB_CLIENT_ENABLED: ""
  CONTINUE_ON_CONNECTOR_FAILURE: ""
  EXPERIMENTAL_CHECKPOINTING_ENABLED: ""
  CONFLUENCE_CONNECTOR_LABELS_TO_SKIP: ""
  JIRA_API_VERSION: ""
  WEB_CONNECTOR_VALIDATE_URLS: ""
  GONG_CONNECTOR_START_TIME: ""
  NOTION_CONNECTOR_ENABLE_RECURSIVE_PAGE_LOOKUP: ""
  MAX_DOCUMENT_CHARS: ""
  MAX_FILE_SIZE_BYTES: ""
  # OnyxBot SlackBot Configs
  DANSWER_BOT_DISABLE_DOCS_ONLY_ANSWER: ""
  DANSWER_BOT_DISPLAY_ERROR_MSGS: ""
  DANSWER_BOT_RESPOND_EVERY_CHANNEL: ""
  DANSWER_BOT_DISABLE_COT: "" # Currently unused
  NOTIFY_SLACKBOT_NO_ANSWER: ""
  # Logging
  # Optional Telemetry, please keep it on (nothing sensitive is collected)? <3
  # https://docs.onyx.app/more/telemetry
  DISABLE_TELEMETRY: ""
  LOG_LEVEL: ""
  LOG_ALL_MODEL_INTERACTIONS: ""
  LOG_DANSWER_MODEL_INTERACTIONS: ""
  LOG_VESPA_TIMING_INFORMATION: ""
  # Shared or Non-backend Related
  INTERNAL_URL: "http://api-server-service:80" # for web server
  WEB_DOMAIN: "http://localhost:3000" # for web server and api server
  DOMAIN: "localhost" # for nginx
  # Chat Configs
  HARD_DELETE_CHATS: ""
