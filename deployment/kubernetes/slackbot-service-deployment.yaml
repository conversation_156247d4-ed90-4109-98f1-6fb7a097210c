apiVersion: apps/v1
kind: Deployment
metadata:
  name: slack-bot-deployment
  labels:
    app: slack-bot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: slack-bot
  template:
    metadata:
      labels:
        app: slack-bot
    spec:
      containers:
        - name: slack-bot
          image: onyxdotapp/onyx-backend:latest
          imagePullPolicy: IfNotPresent
          command: ["python", "onyxdotapp/onyxbot/slack/listener.py"]
          ports:
            - containerPort: 8000
          resources:
            requests:
              cpu: "100m"
              memory: "200Mi"
            limits:
              cpu: "500m"
              memory: "500Mi"
          readinessProbe:
            httpGet:
              path: /metrics
              port: 8000
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /metrics
              port: 8000
            initialDelaySeconds: 15
            periodSeconds: 20

---
apiVersion: v1
kind: Service
metadata:
  name: slack-bot-service
  labels:
    app: slack-bot
spec:
  selector:
    app: slack-bot
  ports:
    # Port exposed for Prometheus metrics
    - protocol: TCP
      port: 8000
      targetPort: 8000
  type: ClusterIP

---
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler

  name: slack-bot-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: slack-bot-deployment
  minReplicas: 1
  maxReplicas: 10
  metrics:
    - type: Pods
      pods:
        metric:
          name: active_tenants
        target:
          type: AverageValue
          averageValue: "40"
