apiVersion: v1
kind: Service
metadata:
  name: indexing-model-server-service
spec:
  selector:
    app: indexing-model-server
  ports:
    - name: indexing-model-server-port
      protocol: TCP
      port: 9000
      targetPort: 9000
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: indexing-model-server-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: indexing-model-server
  template:
    metadata:
      labels:
        app: indexing-model-server
    spec:
      containers:
        - name: indexing-model-server
          image: onyxdotapp/onyx-model-server:latest
          imagePullPolicy: IfNotPresent
          command:
            [
              "uvicorn",
              "model_server.main:app",
              "--host",
              "0.0.0.0",
              "--port",
              "9000",
            ]
          ports:
            - containerPort: 9000
          envFrom:
            - configMapRef:
                name: env-configmap
          env:
            - name: INDEXING_ONLY
              value: "True"
